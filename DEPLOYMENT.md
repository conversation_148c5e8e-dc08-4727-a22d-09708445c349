# Memvid Store 部署指南

## 🚀 快速部署

### 1. 环境要求

#### 必需环境
- **Node.js**: v16.0.0 或更高版本
- **PromptX**: v0.2.0-alpha.10 或更高版本
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+

#### 可选环境
- **FFmpeg**: 用于完整的视频编码功能
- **飞书开发者账号**: 用于飞书API集成

### 2. 安装步骤

#### 步骤 1: 克隆或复制工具文件

确保以下文件存在于您的项目中：
```
.promptx/resource/tool/memvid-store/
├── memvid-store.tool.js      # 工具执行代码
└── memvid-store.manual.md    # 工具使用手册
```

#### 步骤 2: 初始化 PromptX 环境

```bash
# 在项目根目录执行
# 通过 MCP 调用 promptx_init 工具
```

#### 步骤 3: 验证工具注册

```bash
# 通过 MCP 调用 promptx_welcome 工具
# 确认 memvid-store 出现在工具列表中
```

#### 步骤 4: 初始化存储系统

```javascript
// 通过 MCP 调用
{
  "tool": "memvid-store",
  "parameters": {
    "action": "init"
  }
}
```

### 3. FFmpeg 安装（可选但推荐）

#### Windows
```bash
# 使用 Chocolatey
choco install ffmpeg

# 或下载预编译版本
# 从 https://ffmpeg.org/download.html 下载
# 解压并添加到 PATH 环境变量
```

#### macOS
```bash
# 使用 Homebrew
brew install ffmpeg
```

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install ffmpeg
```

#### 验证安装
```bash
ffmpeg -version
```

## ⚙️ 配置选项

### 1. 基础配置

工具支持以下配置参数：

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| compression_level | number | 5 | 压缩级别 (1-10) |
| max_results | number | 10 | 搜索结果上限 |
| storage_path | string | ".memvid" | 存储目录路径 |

### 2. 存储配置

```javascript
// 自定义存储配置示例
{
  "action": "store",
  "content": "内容...",
  "compression_level": 8,  // 高压缩比
  "metadata": {
    "retention_days": 365,  // 保留天数
    "backup_enabled": true  // 启用备份
  }
}
```

### 3. 搜索配置

```javascript
// 自定义搜索配置示例
{
  "action": "search",
  "query": "搜索词",
  "max_results": 20,      // 增加结果数量
  "include_metadata": true // 包含元数据
}
```

## 🔧 高级配置

### 1. 飞书API集成

#### 获取飞书API凭证

1. 访问 [飞书开放平台](https://open.feishu.cn/)
2. 创建应用并获取 App ID 和 App Secret
3. 配置应用权限：
   - `docs:read` - 读取文档权限
   - `docs:write` - 写入文档权限

#### 配置环境变量

```bash
# 在 .env 文件中添加
FEISHU_APP_ID=your_app_id
FEISHU_APP_SECRET=your_app_secret
FEISHU_BASE_URL=https://open.feishu.cn
```

### 2. 性能优化配置

#### 内存优化
```javascript
// 大文件处理配置
{
  "max_content_size": 1048576,  // 1MB
  "chunk_size": 65536,          // 64KB 分块
  "enable_streaming": true      // 启用流式处理
}
```

#### 存储优化
```javascript
// 存储优化配置
{
  "compression_algorithm": "gzip",
  "index_strategy": "btree",
  "cache_size": 100,           // 缓存条目数
  "auto_cleanup": true         // 自动清理
}
```

## 📁 目录结构

### 默认目录结构
```
项目根目录/
├── .memvid/                 # 主存储目录
│   ├── videos/              # 视频文件存储
│   ├── qr/                  # QR码图片存储
│   ├── db/                  # 数据库文件
│   │   └── records.json     # 主记录文件
│   ├── cache/               # 缓存目录
│   └── logs/                # 日志文件
├── .promptx/                # PromptX 配置
│   └── resource/tool/memvid-store/
└── README.md
```

### 自定义目录配置
```javascript
// 环境变量配置
process.env.MEMVID_STORAGE_PATH = "/custom/path/.memvid"
process.env.MEMVID_CACHE_PATH = "/custom/cache"
process.env.MEMVID_LOG_PATH = "/custom/logs"
```

## 🔒 安全配置

### 1. 数据加密

```javascript
// 启用数据加密（未来功能）
{
  "encryption": {
    "enabled": true,
    "algorithm": "AES-256-GCM",
    "key_rotation": "monthly"
  }
}
```

### 2. 访问控制

```javascript
// 访问控制配置（未来功能）
{
  "access_control": {
    "enabled": true,
    "auth_required": true,
    "rate_limiting": {
      "requests_per_minute": 100
    }
  }
}
```

## 📊 监控和日志

### 1. 性能监控

```javascript
// 启用性能监控
{
  "monitoring": {
    "enabled": true,
    "metrics": ["response_time", "storage_usage", "search_performance"],
    "export_interval": 60000  // 60秒
  }
}
```

### 2. 日志配置

```javascript
// 日志级别配置
{
  "logging": {
    "level": "info",           // debug, info, warn, error
    "file_rotation": true,
    "max_file_size": "10MB",
    "max_files": 5
  }
}
```

## 🔄 备份和恢复

### 1. 自动备份

```bash
# 创建备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf "memvid_backup_$DATE.tar.gz" .memvid/
```

### 2. 数据恢复

```bash
# 恢复数据
tar -xzf memvid_backup_20240115_120000.tar.gz
```

### 3. 增量备份

```javascript
// 增量备份配置（未来功能）
{
  "backup": {
    "enabled": true,
    "type": "incremental",
    "schedule": "0 2 * * *",    // 每天凌晨2点
    "retention_days": 30
  }
}
```

## 🚨 故障排除

### 常见问题

#### 1. FFmpeg 未找到
```
错误: FFMPEG_NOT_FOUND
解决: 安装 FFmpeg 并确保在 PATH 中
```

#### 2. 权限错误
```
错误: EACCES: permission denied
解决: 检查 .memvid 目录权限
chmod 755 .memvid
```

#### 3. 存储空间不足
```
错误: ENOSPC: no space left on device
解决: 清理磁盘空间或更改存储路径
```

### 诊断命令

```bash
# 检查系统状态
node -v                    # Node.js 版本
ffmpeg -version           # FFmpeg 版本
ls -la .memvid/           # 存储目录状态
du -sh .memvid/           # 存储空间使用
```

## 🔧 维护

### 定期维护任务

1. **清理过期数据**: 定期清理不需要的记录
2. **优化索引**: 重建搜索索引提升性能
3. **备份验证**: 验证备份文件完整性
4. **性能监控**: 检查系统性能指标

### 维护脚本示例

```bash
#!/bin/bash
# memvid-maintenance.sh

echo "开始 Memvid Store 维护..."

# 清理临时文件
find .memvid/cache -type f -mtime +7 -delete

# 压缩日志文件
gzip .memvid/logs/*.log

# 检查存储使用情况
du -sh .memvid/

echo "维护完成!"
```

## 📈 性能调优

### 1. 存储优化
- 选择合适的压缩级别
- 定期清理不需要的文件
- 使用SSD存储提升I/O性能

### 2. 搜索优化
- 建立合适的索引
- 使用精确的搜索关键词
- 限制搜索结果数量

### 3. 内存优化
- 控制缓存大小
- 及时释放不用的资源
- 监控内存使用情况
