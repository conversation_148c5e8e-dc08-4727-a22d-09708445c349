# Memvid Store 使用示例

## 🎯 完整使用流程演示

### 1. 初始化系统

首先初始化 Memvid 存储系统：

```javascript
// 通过 PromptX MCP 调用
{
  "action": "init"
}
```

**预期结果**：
```json
{
  "success": true,
  "data": {
    "message": "Memvid 存储系统初始化成功",
    "storage_path": ".memvid",
    "ffmpeg_available": false  // 如果未安装FFmpeg
  }
}
```

### 2. 存储AI对话记录

存储一个重要的AI对话：

```javascript
{
  "action": "store",
  "content": "用户询问了关于React性能优化的问题。我建议使用以下方法：1. 使用React.memo包装组件避免不必要的重渲染；2. 使用useMemo缓存计算结果；3. 使用useCallback缓存函数引用；4. 合理使用懒加载和代码分割。这些技术可以显著提升React应用的性能。",
  "metadata": {
    "timestamp": "2024-01-15T14:30:00Z",
    "user_id": "developer_001",
    "conversation_id": "react_optimization_conv",
    "topic": "React性能优化",
    "importance": "high"
  },
  "tags": ["react", "performance", "optimization", "memo", "hooks"],
  "source": "ai_conversation",
  "compression_level": 7
}
```

**预期结果**：
```json
{
  "success": true,
  "data": {
    "id": "memvid_20240115_143000_abc123",
    "video_path": ".memvid/videos/memvid_20240115_143000_abc123.mp4",
    "qr_path": ".memvid/qr/memvid_20240115_143000_abc123.png",
    "compression_ratio": "70.0%",
    "storage_size": "89 Bytes",
    "original_size": "297 Bytes",
    "processing_time_ms": 15
  }
}
```

### 3. 存储项目文档

存储项目相关的重要文档：

```javascript
{
  "action": "store",
  "content": "项目架构决策：我们决定采用微服务架构，主要原因包括：1. 团队规模扩大，需要独立开发和部署；2. 不同服务有不同的技术栈需求；3. 需要更好的容错性和可扩展性。技术栈选择：前端使用React+TypeScript，后端使用Node.js+Express，数据库使用PostgreSQL，消息队列使用Redis。",
  "metadata": {
    "timestamp": "2024-01-15T10:00:00Z",
    "author": "tech_lead",
    "document_type": "architecture_decision",
    "project": "memvid_store",
    "version": "1.0"
  },
  "tags": ["architecture", "microservices", "tech-stack", "decision"],
  "source": "project_documentation",
  "compression_level": 6
}
```

### 4. 同步飞书文档

从飞书同步重要文档：

```javascript
{
  "action": "sync",
  "feishu_doc_id": "doccnMemvidStoreProject2024"
}
```

**预期结果**：
```json
{
  "success": true,
  "data": {
    "message": "飞书文档同步成功",
    "doc_id": "doccnMemvidStoreProject2024",
    "local_id": "memvid_20240115_143500_def456",
    "sync_time_ms": 120,
    "content_preview": "Memvid Store 项目规划文档..."
  }
}
```

### 5. 搜索相关内容

#### 按关键词搜索
```javascript
{
  "action": "search",
  "query": "React性能",
  "max_results": 5
}
```

#### 按标签搜索
```javascript
{
  "action": "search",
  "query": "architecture",
  "max_results": 10
}
```

#### 复合搜索
```javascript
{
  "action": "search",
  "query": "微服务 架构",
  "max_results": 3
}
```

**搜索结果示例**：
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": "memvid_20240115_100000_xyz789",
        "content": "项目架构决策：我们决定采用微服务架构，主要原因包括：1. 团队规模扩大，需要独立开发和部署；2. 不同服务有不同的技术栈需求...",
        "metadata": {
          "author": "tech_lead",
          "document_type": "architecture_decision"
        },
        "tags": ["architecture", "microservices", "tech-stack"],
        "relevance_score": 0.95,
        "timestamp": "2024-01-15T10:00:00Z"
      }
    ],
    "total_found": 1,
    "search_time_ms": 8,
    "query": "微服务 架构"
  }
}
```

## 🔄 批量操作示例

### 批量存储多个对话记录

```javascript
// 第一条记录
{
  "action": "store",
  "content": "用户问题：如何优化数据库查询性能？回答：1. 添加适当的索引；2. 优化查询语句；3. 使用查询缓存；4. 考虑读写分离。",
  "tags": ["database", "performance", "optimization"],
  "source": "ai_conversation"
}

// 第二条记录
{
  "action": "store", 
  "content": "用户问题：什么是Docker容器化？回答：Docker是一种轻量级的虚拟化技术，可以将应用程序及其依赖打包成容器，实现一次构建，到处运行。",
  "tags": ["docker", "containerization", "devops"],
  "source": "ai_conversation"
}
```

## 📊 性能测试示例

### 大量数据存储测试

存储较大的文档内容：

```javascript
{
  "action": "store",
  "content": "这是一个包含大量技术细节的文档..." + "重复内容".repeat(1000),
  "metadata": {
    "test_type": "performance",
    "content_size": "large"
  },
  "tags": ["performance-test", "large-content"],
  "compression_level": 9
}
```

### 搜索性能测试

在大量数据中进行搜索：

```javascript
{
  "action": "search",
  "query": "性能优化",
  "max_results": 50
}
```

## 🛠️ 故障排除示例

### 处理存储失败

```javascript
// 如果存储失败，检查错误信息
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "参数验证失败",
    "details": "内容长度不能超过 1MB"
  }
}
```

### 处理搜索无结果

```javascript
{
  "success": true,
  "data": {
    "results": [],
    "total_found": 0,
    "search_time_ms": 2,
    "query": "不存在的内容"
  }
}
```

## 🎯 最佳实践

1. **合理使用标签**：为每条记录添加相关标签，提升搜索效果
2. **元数据完整性**：提供完整的元数据信息，便于后续管理
3. **压缩级别选择**：根据内容重要性选择合适的压缩级别
4. **定期备份**：定期备份 `.memvid` 目录
5. **搜索优化**：使用具体的关键词进行搜索，避免过于宽泛的查询

## 🔮 高级用法

### 与其他系统集成

```javascript
// 结合时间戳进行时间范围搜索（未来功能）
{
  "action": "search",
  "query": "React",
  "filters": {
    "date_range": {
      "start": "2024-01-01",
      "end": "2024-01-31"
    },
    "source": "ai_conversation"
  }
}
```

### 自动化工作流

```javascript
// 自动同步和存储工作流（未来功能）
{
  "action": "auto_sync",
  "schedule": "daily",
  "sources": ["feishu", "notion", "confluence"]
}
```
