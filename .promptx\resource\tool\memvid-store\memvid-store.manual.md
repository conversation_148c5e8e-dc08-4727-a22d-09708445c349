<manual>
<identity>
## 工具名称
@tool://memvid-store

## 简介
基于 Memvid 原理的高效本地数据存储工具，将文本数据编码为视频格式实现压缩存储和快速语义检索
</identity>

<purpose>
⚠️ **AI重要提醒**: 调用此工具前必须完整阅读本说明书，理解工具功能边界、参数要求和使用限制。禁止在不了解工具功能的情况下盲目调用。

## 核心问题定义
解决 AI 系统中大量文本数据的高效存储和快速检索问题，特别是减少 AI 幻觉通过存储真实可靠的信息源。

## 价值主张
- 🎯 **解决什么痛点**：传统向量数据库占用大量内存和存储空间，检索速度慢
- 🚀 **带来什么价值**：通过视频编码实现 10-100 倍的存储压缩，亚秒级检索速度
- 🌟 **独特优势**：结合 FFmpeg 压缩技术和 QR 码编码，实现极致的存储效率

## 应用边界
- ✅ **适用场景**：AI 对话记录存储、知识库构建、文档内容归档、飞书文档同步
- ❌ **不适用场景**：实时流数据处理、超大文件存储（>100MB单文件）、需要频繁修改的数据
</purpose>

<usage>
## 使用时机
- 需要存储 AI 生成的重要信息时
- 构建本地知识库避免 AI 幻觉时
- 同步飞书文档到本地存储时
- 需要快速检索历史对话记录时

## 操作步骤
1. **存储阶段**：调用 store 操作，传入文本内容和元数据
2. **检索阶段**：调用 search 操作，使用关键词或语义查询
3. **同步阶段**：调用 sync 操作，与飞书文档进行双向同步

## 最佳实践
- 🎯 **效率提升**：批量存储多条记录可提升编码效率
- ⚠️ **避免陷阱**：避免存储过于频繁变化的数据
- 🔧 **故障排除**：检查 FFmpeg 是否正确安装，确保有足够磁盘空间

## 注意事项
- 需要系统安装 FFmpeg
- 首次使用会创建本地数据库和索引
- 视频文件存储在 `.memvid` 目录下
</usage>

<parameter>
## 必需参数
| 参数名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| action | string | 操作类型：store/search/sync | "store" |

## 条件必需参数
| 参数名 | 类型 | 条件 | 描述 | 示例 |
|--------|------|------|------|------|
| content | string | action=store | 要存储的文本内容 | "这是重要的AI对话记录" |
| query | string | action=search | 搜索查询 | "AI对话记录" |
| feishu_doc_id | string | action=sync | 飞书文档ID | "doccnxxxxxx" |

## 可选参数
| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| metadata | object | {} | 附加元数据信息 |
| tags | array | [] | 标签列表 |
| source | string | "manual" | 数据来源标识 |
| compression_level | number | 5 | 压缩级别 1-10 |
| max_results | number | 10 | 最大返回结果数 |

## 参数约束
- **内容长度**：单次存储内容不超过 1MB
- **查询长度**：搜索查询不超过 1000 字符
- **标签数量**：每条记录最多 20 个标签

## 参数示例
```json
{
  "action": "store",
  "content": "用户询问了关于 React 组件优化的问题，我建议使用 React.memo 和 useMemo 来优化性能...",
  "metadata": {
    "timestamp": "2024-01-01T12:00:00Z",
    "user_id": "user123",
    "conversation_id": "conv456"
  },
  "tags": ["react", "optimization", "performance"],
  "source": "ai_conversation"
}
```
</parameter>

<outcome>
## 成功返回格式
### 存储操作成功
```json
{
  "success": true,
  "data": {
    "id": "memvid_20240101_120000_abc123",
    "video_path": ".memvid/videos/20240101_120000_abc123.mp4",
    "compression_ratio": 85.6,
    "storage_size": "2.3KB",
    "original_size": "16KB"
  }
}
```

### 搜索操作成功
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": "memvid_20240101_120000_abc123",
        "content": "匹配的文本内容...",
        "metadata": {...},
        "relevance_score": 0.95,
        "tags": ["react", "optimization"]
      }
    ],
    "total_found": 5,
    "search_time_ms": 45
  }
}
```

## 错误处理格式
```json
{
  "success": false,
  "error": {
    "code": "FFMPEG_NOT_FOUND",
    "message": "FFmpeg 未安装或不在 PATH 中",
    "details": "请安装 FFmpeg 并确保可在命令行中访问"
  }
}
```

## 结果解读指南
- **compression_ratio**：压缩比例，数值越高表示压缩效果越好
- **relevance_score**：相关性评分，0-1 之间，越接近 1 表示越相关
- **search_time_ms**：搜索耗时，通常应在 100ms 以内

## 后续动作建议
- 存储成功后可以立即进行搜索验证
- 定期运行优化命令整理存储空间
- 建议设置定时任务同步飞书文档
</outcome>
</manual>
